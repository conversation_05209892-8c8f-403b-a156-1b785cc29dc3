# Creative Hydraulics Admin Panel

A professional admin interface for managing custom products on the Creative Hydraulics website.

## Features

- **Secure Login System**: Simple authentication with session management
- **Product Management**: Add, view, and delete custom products
- **Image Upload**: Upload product images with drag-and-drop support
- **Firebase Integration**: Real-time database and cloud storage
- **Responsive Design**: Works on desktop and mobile devices
- **Professional UI**: Clean, modern interface matching the main website

## Getting Started

### 1. Access the Admin Panel

Navigate to: `admin/login.html`

**Login Credentials:**
- Username: `admin`
- Password: `creative2024`

### 2. Adding Products

1. After logging in, you'll see the product management interface
2. Fill in the required fields:
   - **Product Name**: Enter a descriptive name for the product
   - **Product Description**: Provide detailed information about the product
   - **Product Image**: Upload an image (PNG, JPG, JPEG up to 5MB)

3. Click "Add Product" to save

### 3. Managing Products

- **View Recent Products**: See the 5 most recently added products
- **Delete Products**: Click the trash icon to remove products
- **View Live Site**: Click "View Products Page" to see how products appear on the website

## Technical Details

### Firebase Configuration

The admin panel uses Firebase Realtime Database and Storage:
- **Database URL**: `https://chwk-edca8-default-rtdb.firebaseio.com/`
- **Project ID**: `chwk-edca8`
- **Storage**: Images are stored in Firebase Cloud Storage

### File Structure

```
admin/
├── index.html          # Main admin panel
├── login.html          # Login page
├── admin.js           # Admin panel functionality
└── README.md          # This file
```

### Security Features

- Session-based authentication
- 24-hour session timeout
- Input validation
- File type and size restrictions
- Secure Firebase rules (should be configured)

## Product Display

Products added through the admin panel will automatically appear in the "Custom Build" section of the main products page (`pages/products.html`).

### Custom Build Section Features

- **Real-time Updates**: Products appear immediately after being added
- **Professional Layout**: Matches the existing product grid design
- **Responsive Design**: Works on all device sizes
- **Loading States**: Shows loading spinner while fetching data
- **Error Handling**: Graceful error messages if data fails to load

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Security Recommendations

For production use, consider implementing:

1. **Proper Authentication**: Replace simple username/password with OAuth or JWT
2. **Firebase Security Rules**: Restrict database and storage access
3. **HTTPS**: Ensure all connections are encrypted
4. **Input Sanitization**: Additional server-side validation
5. **Rate Limiting**: Prevent abuse of the upload functionality

## Troubleshooting

### Common Issues

1. **Images not uploading**: Check file size (max 5MB) and format (PNG, JPG, JPEG)
2. **Products not appearing**: Check browser console for Firebase errors
3. **Login issues**: Ensure correct credentials and clear browser cache
4. **Session expired**: Re-login if session has been inactive for 24 hours

### Support

For technical support or questions, contact the development team.

---

© 2024 Creative Hydraulics. All rights reserved.
