<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Admin - Creative Hydraulics</title>
    <link rel="icon" type="image/png" href="../assets/logos/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Outfit:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Jura:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Michroma&display=swap" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'display': ['Outfit', 'sans-serif'],
                        'Jura': ['Jura', 'sans-serif'],
                        'Michroma': ['Michroma', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style>
        .font-Michroma { font-family: 'Michroma', sans-serif; }
        .font-Jura { font-family: 'Jura', sans-serif; }
        
        .upload-area {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #0047AB;
            background-color: #f8fafc;
        }
        
        .upload-area.dragover {
            border-color: #0047AB;
            background-color: #eff6ff;
        }
        
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-flex;
        }
        
        .success-message {
            display: none;
        }
        
        .success-message.show {
            display: block;
        }
    </style>
</head>

<body class="font-body bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="../assets/logos/logo.png" alt="Creative Hydraulics Logo" class="w-12 h-12 object-contain" />
                    <div>
                        <h1 class="text-xl font-Michroma font-bold text-gray-900">Creative Hydraulics</h1>
                        <p class="text-sm font-Jura text-gray-600">Product Management</p>
                    </div>
                </div>
                <a href="../pages/products.html" target="_blank" class="bg-[#0047AB] hover:bg-[#003A8C] text-white px-6 py-2 rounded-lg font-Jura font-medium transition-colors shadow-lg">
                    <i class="fas fa-external-link-alt mr-2"></i>View Products Page
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Page Title -->
            <div class="text-center mb-8">
                <h2 class="text-3xl font-Michroma font-bold text-gray-900 mb-2">Add Custom Product</h2>
                <p class="text-gray-600 font-Jura">Create new products for the Custom Build section</p>
                <div class="w-20 h-0.5 bg-[#D22B2B] rounded-full mx-auto mt-4"></div>
            </div>

            <!-- Success Message -->
            <div id="successMessage" class="success-message bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Product added successfully!</span>
                </div>
            </div>

            <!-- Firebase Status -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-database text-blue-600 mr-2"></i>
                        <span class="font-Jura font-medium text-blue-800">Firebase Status:</span>
                        <span id="firebaseStatus" class="ml-2 font-Jura text-blue-600">Checking...</span>
                    </div>
                    <button id="testFirebase" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-Jura">
                        Test Connection
                    </button>
                </div>
            </div>

            <!-- Add Product Form -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h3 class="text-xl font-Michroma font-bold text-gray-900 mb-6">Add New Product</h3>
                
                <form id="productForm" class="space-y-6">
                    <!-- Product Name -->
                    <div>
                        <label for="productName" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="productName" name="productName" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura"
                               placeholder="Enter product name">
                    </div>

                    <!-- Product Description -->
                    <div>
                        <label for="productDescription" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Description <span class="text-red-500">*</span>
                        </label>
                        <textarea id="productDescription" name="productDescription" required rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura resize-none"
                                  placeholder="Enter detailed product description"></textarea>
                    </div>

                    <!-- Product Image -->
                    <div>
                        <label class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Image <span class="text-red-500">*</span>
                        </label>
                        <div id="uploadArea" class="upload-area p-8 rounded-lg text-center cursor-pointer">
                            <div id="uploadContent">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600 font-Jura mb-2">Click to upload or drag and drop</p>
                                <p class="text-sm text-gray-500 font-Jura">PNG, JPG, JPEG up to 5MB</p>
                            </div>
                            <div id="imagePreview" class="hidden">
                                <img id="previewImg" class="preview-image mx-auto mb-4" alt="Preview">
                                <p class="text-sm text-gray-600 font-Jura">Click to change image</p>
                            </div>
                        </div>
                        <input type="file" id="imageInput" accept="image/*" class="hidden">
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4">
                        <button type="button" id="resetBtn" 
                                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-Jura font-medium transition-colors">
                            Reset Form
                        </button>
                        <button type="submit" id="submitBtn"
                                class="px-6 py-3 bg-[#0047AB] hover:bg-[#003A8C] text-white rounded-lg font-Jura font-medium transition-colors">
                            <span id="submitText">Add Product</span>
                            <span id="loadingSpinner" class="loading items-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Adding...
                            </span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Recent Products -->
            <div class="bg-white rounded-xl shadow-lg p-8 mt-8">
                <h3 class="text-xl font-Michroma font-bold text-gray-900 mb-6">Recent Products</h3>
                <div id="recentProducts" class="space-y-4">
                    <p class="text-gray-500 font-Jura text-center py-8">No products added yet</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC8aAJOv3r43YscYB9A9jZFadRn_1GRG7E",
            authDomain: "chwk-edca8.firebaseapp.com",
            projectId: "chwk-edca8",
            storageBucket: "chwk-edca8.firebasestorage.app",
            messagingSenderId: "456605094673",
            appId: "1:456605094673:web:609879171843944d1db2b0",
            databaseURL: "https://chwk-edca8-default-rtdb.firebaseio.com/"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();
        const storage = firebase.storage();

        console.log('Firebase initialized successfully with compat SDK');

        // Make Firebase available globally for admin.js
        window.firebaseDatabase = database;
        window.firebaseStorage = storage;
        window.firebase = firebase;
    </script>

    <!-- Admin Panel JavaScript -->
    <script src="admin.js"></script>
</body>
</html>
